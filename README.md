# Onta 商户管理后台

这是一个使用现代化技术栈构建的商户管理后台前端项目，展示了 React 生态系统中最佳实践的完整实现。

## ✨ 特性

- 🚀 **现代化技术栈** - React 19 + TypeScript + Vite
- 🎯 **类型安全** - 完整的 TypeScript 支持
- 🔄 **状态管理** - Zustand 轻量级状态管理
- 📡 **数据获取** - TanStack Query 强大的数据同步
- 🛣️ **路由管理** - TanStack Router 类型安全路由
- 🎨 **UI 组件** - Tailwind CSS + DaisyUI 美观界面
- 📱 **响应式设计** - 移动端友好
- 🔧 **开发体验** - 热重载、ESLint、TypeScript 检查

## 技术栈

- **React** - 用户界面库
- **TypeScript** - 类型安全的 JavaScript 超集
- **Vite** - 快速的前端构建工具
- **TanStack Router** - 类型安全的路由库
- **Zustand** - 简单的状态管理库
- **TanStack Query** - 数据获取和缓存库
- **Tailwind CSS** - 实用优先的 CSS 框架
- **DaisyUI** - 基于 Tailwind CSS 的组件库

## 项目结构

```
src/
├── api/          # API 请求函数
├── assets/       # 静态资源
├── components/   # 可复用组件
├── hooks/        # 自定义 React hooks
├── layouts/      # 布局组件
├── pages/        # 页面组件
├── stores/       # Zustand 状态管理
├── types/        # TypeScript 类型定义
├── utils/        # 工具函数
├── App.tsx       # 应用入口组件
├── main.tsx      # 应用入口文件
├── router.tsx    # 路由配置
└── index.css     # 全局样式
```

## 🎯 功能演示

项目包含完整的示例演示：

### Zustand 状态管理示例

- **基础示例** (`/counter`) - 简单计数器，展示基本用法和 immer 中间件
- **完整示例** (`/zustand-demo`) - 展示持久化存储、中间件使用、选择器优化等高级特性
  - 用户状态管理（持久化到 localStorage）
  - 主题切换（自动检测系统主题）
  - 商户管理（复杂状态操作、筛选、搜索）
  - 状态订阅和计算属性

### React Query 数据获取示例

- **基础示例** (`/todos`) - 待办事项 CRUD 操作
- **完整示例** (`/react-query-demo`) - 展示高级特性
  - 乐观更新（立即更新 UI，失败时回滚）
  - 错误处理（重试机制、错误边界）
  - 无限滚动（分页数据加载）
  - 数据预取（鼠标悬停预取）
  - 缓存管理（智能失效和更新）

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:5173](http://localhost:5173) 查看应用。

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

### 代码检查

```bash
npm run lint
```

## 📚 最佳实践指南

### 状态管理 (Zustand)

### 创建 Store

```typescript
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

type MyState = {
  // 状态
  count: number;
  // 操作
  increment: () => void;
};

export const useMyStore = create<MyState>(
  immer(set => ({
    count: 0,
    increment: () =>
      set(state => {
        state.count += 1;
      }),
  }))
);
```

### 在组件中使用

```typescript
import { useMyStore } from "../stores/myStore";

function MyComponent() {
  const { count, increment } = useMyStore();

  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={increment}>Increment</button>
    </div>
  );
}
```

### 数据获取 (React Query)

### 创建 API 函数

```typescript
export const fetchData = async (): Promise<Data[]> => {
  const response = await fetch('https://api.example.com/data');
  if (!response.ok) {
    throw new Error('Failed to fetch data');
  }
  return response.json();
};
```

### 创建自定义 Hook

```typescript
import { useQuery } from '@tanstack/react-query';
import { fetchData } from '../api/dataApi';

export const useData = () => {
  return useQuery({
    queryKey: ['data'],
    queryFn: fetchData,
  });
};
```

### 在组件中使用

```typescript
import { useData } from "../hooks/useData";

function DataComponent() {
  const { data, isLoading, isError } = useData();

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error loading data</div>;

  return (
    <div>
      {data.map((item) => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  );
}
```

### 项目结构说明

#### 状态管理 (`src/stores/`)

- `counterStore.ts` - 基础计数器状态
- `userStore.ts` - 用户状态（支持持久化）
- `themeStore.ts` - 主题状态（自动检测系统主题）
- `merchantStore.ts` - 商户管理状态（复杂业务逻辑）

#### API 层 (`src/api/`)

- `todoApi.ts` - 待办事项 API
- `postsApi.ts` - 文章 API（包含错误模拟）

#### 自定义 Hooks (`src/hooks/`)

- `useTodos.ts` - 待办事项相关 hooks
- `usePosts.ts` - 文章相关 hooks（包含高级特性）

#### 工具函数 (`src/utils/`)

- `format.ts` - 格式化工具（货币、日期、文件大小等）
- `validation.ts` - 验证工具（邮箱、手机号、密码强度等）
- `storage.ts` - 本地存储管理

#### 类型定义 (`src/types/`)

- `common.ts` - 通用类型定义
- `index.ts` - 类型导出

## 🔧 开发工具配置

### ESLint 配置

项目使用 ESLint 进行代码质量检查，配置文件：`eslint.config.js`

### TypeScript 配置

- `tsconfig.json` - 主配置文件
- `tsconfig.app.json` - 应用配置
- `tsconfig.node.json` - Node.js 配置

### Tailwind CSS 配置

- `tailwind.config.js` - Tailwind 配置
- `postcss.config.js` - PostCSS 配置

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

MIT License - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢以下开源项目：

- [React](https://reactjs.org/)
- [TanStack Router](https://tanstack.com/router)
- [TanStack Query](https://tanstack.com/query)
- [Zustand](https://github.com/pmndrs/zustand)
- [Tailwind CSS](https://tailwindcss.com/)
- [DaisyUI](https://daisyui.com/)
- [Vite](https://vitejs.dev/)
