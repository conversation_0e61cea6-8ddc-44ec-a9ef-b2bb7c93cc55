// 本地存储工具函数

/**
 * 本地存储管理器
 */
export class StorageManager {
  private prefix: string;

  constructor(prefix: string = 'onta_') {
    this.prefix = prefix;
  }

  /**
   * 获取完整的键名
   * @param key 键名
   */
  private getKey(key: string): string {
    return `${this.prefix}${key}`;
  }

  /**
   * 设置 localStorage 项
   * @param key 键名
   * @param value 值
   */
  setLocal<T>(key: string, value: T): void {
    try {
      const serializedValue = JSON.stringify(value);
      localStorage.setItem(this.getKey(key), serializedValue);
    } catch (error) {
      console.error('Failed to set localStorage item:', error);
    }
  }

  /**
   * 获取 localStorage 项
   * @param key 键名
   * @param defaultValue 默认值
   */
  getLocal<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = localStorage.getItem(this.getKey(key));
      if (item === null) {
        return defaultValue ?? null;
      }
      return JSON.parse(item);
    } catch (error) {
      console.error('Failed to get localStorage item:', error);
      return defaultValue ?? null;
    }
  }

  /**
   * 删除 localStorage 项
   * @param key 键名
   */
  removeLocal(key: string): void {
    try {
      localStorage.removeItem(this.getKey(key));
    } catch (error) {
      console.error('Failed to remove localStorage item:', error);
    }
  }

  /**
   * 设置 sessionStorage 项
   * @param key 键名
   * @param value 值
   */
  setSession<T>(key: string, value: T): void {
    try {
      const serializedValue = JSON.stringify(value);
      sessionStorage.setItem(this.getKey(key), serializedValue);
    } catch (error) {
      console.error('Failed to set sessionStorage item:', error);
    }
  }

  /**
   * 获取 sessionStorage 项
   * @param key 键名
   * @param defaultValue 默认值
   */
  getSession<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = sessionStorage.getItem(this.getKey(key));
      if (item === null) {
        return defaultValue ?? null;
      }
      return JSON.parse(item);
    } catch (error) {
      console.error('Failed to get sessionStorage item:', error);
      return defaultValue ?? null;
    }
  }

  /**
   * 删除 sessionStorage 项
   * @param key 键名
   */
  removeSession(key: string): void {
    try {
      sessionStorage.removeItem(this.getKey(key));
    } catch (error) {
      console.error('Failed to remove sessionStorage item:', error);
    }
  }

  /**
   * 清空所有带前缀的 localStorage 项
   */
  clearLocal(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error('Failed to clear localStorage:', error);
    }
  }

  /**
   * 清空所有带前缀的 sessionStorage 项
   */
  clearSession(): void {
    try {
      const keys = Object.keys(sessionStorage);
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          sessionStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error('Failed to clear sessionStorage:', error);
    }
  }
}

// 创建默认的存储管理器实例
export const storage = new StorageManager();

// 便捷函数
export const {
  setLocal,
  getLocal,
  removeLocal,
  setSession,
  getSession,
  removeSession,
  clearLocal,
  clearSession,
} = storage;
