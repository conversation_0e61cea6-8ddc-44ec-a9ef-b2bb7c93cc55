import type { ReactNode } from 'react';

interface WelcomeLayoutProps {
  children: ReactNode;
}

export function WelcomeLayout({ children }: WelcomeLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center mr-3">
                  <span className="text-white font-bold text-sm">O</span>
                </div>
                <span className="text-xl font-semibold text-gray-900">Onta Network</span>
              </div>
            </div>

            {/* Right side - Acquiring badge and user info */}
            <div className="flex items-center space-x-4">
              {/* Acquiring badge */}
              <div className="bg-gray-100 px-3 py-1 rounded-md">
                <span className="text-sm text-gray-600">📄 Acquiring</span>
              </div>

              {/* User dropdown */}
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-sm text-gray-600">👤</span>
                </div>
                <span className="text-sm text-gray-700">Tester 01</span>
                <svg
                  className="w-4 h-4 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="flex-1">{children}</main>
    </div>
  );
}
