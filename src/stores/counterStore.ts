import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

type CounterState = {
  count: number;
  increment: () => void;
  decrement: () => void;
  reset: () => void;
  incrementBy: (value: number) => void;
};

// 使用immer中间件简化状态更新逻辑
export const useCounterStore = create<CounterState>()(
  immer(set => ({
    count: 0,
    increment: () =>
      set(state => {
        state.count += 1;
      }),
    decrement: () =>
      set(state => {
        state.count -= 1;
      }),
    reset: () => set({ count: 0 }),
    incrementBy: (value: number) =>
      set(state => {
        state.count += value;
      }),
  }))
);
