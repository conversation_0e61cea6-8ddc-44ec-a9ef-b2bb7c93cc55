import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { User } from '../types';
import { UserRole } from '../types';

// 用户状态接口
interface UserState {
  // 状态
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;

  // 操作
  login: (user: User) => void;
  logout: () => void;
  updateUser: (updates: Partial<User>) => void;
  setLoading: (loading: boolean) => void;
}

// 用户状态管理
export const useUserStore = create<UserState>()(
  persist(
    immer(set => ({
      // 初始状态
      user: null,
      isAuthenticated: false,
      isLoading: false,

      // 登录
      login: (user: User) => {
        set(state => {
          state.user = user;
          state.isAuthenticated = true;
          state.isLoading = false;
        });
      },

      // 登出
      logout: () => {
        set(state => {
          state.user = null;
          state.isAuthenticated = false;
          state.isLoading = false;
        });
      },

      // 更新用户信息
      updateUser: (updates: Partial<User>) => {
        set(state => {
          if (state.user) {
            Object.assign(state.user, updates);
          }
        });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set(state => {
          state.isLoading = loading;
        });
      },
    })),
    {
      name: 'user-storage', // 存储键名
      storage: createJSONStorage(() => localStorage), // 使用 localStorage
      partialize: state => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }), // 只持久化部分状态
    }
  )
);

// 选择器函数（用于性能优化）
export const useUser = () => useUserStore(state => state.user);
export const useIsAuthenticated = () => useUserStore(state => state.isAuthenticated);
export const useUserLoading = () => useUserStore(state => state.isLoading);

// 计算属性选择器
export const useIsAdmin = () => useUserStore(state => state.user?.role === UserRole.ADMIN);

export const useIsMerchant = () => useUserStore(state => state.user?.role === UserRole.MERCHANT);

// 操作选择器
export const useUserActions = () =>
  useUserStore(state => ({
    login: state.login,
    logout: state.logout,
    updateUser: state.updateUser,
    setLoading: state.setLoading,
  }));
