// 文章 API 请求函数

export type Post = {
  id: number;
  title: string;
  body: string;
  userId: number;
};

export type CreatePostData = Omit<Post, 'id'>;

// 获取文章列表
export const fetchPosts = async (page: number = 1, limit: number = 10): Promise<Post[]> => {
  const response = await fetch(
    `https://jsonplaceholder.typicode.com/posts?_page=${page}&_limit=${limit}`
  );
  if (!response.ok) {
    throw new Error('Failed to fetch posts');
  }
  return response.json();
};

// 获取单个文章
export const fetchPost = async (id: number): Promise<Post> => {
  const response = await fetch(`https://jsonplaceholder.typicode.com/posts/${id}`);
  if (!response.ok) {
    throw new Error(`Failed to fetch post with id ${id}`);
  }
  return response.json();
};

// 创建文章
export const createPost = async (data: CreatePostData): Promise<Post> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000));

  const response = await fetch('https://jsonplaceholder.typicode.com/posts', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error('Failed to create post');
  }

  return response.json();
};

// 更新文章
export const updatePost = async (post: Post): Promise<Post> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 800));

  const response = await fetch(`https://jsonplaceholder.typicode.com/posts/${post.id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(post),
  });

  if (!response.ok) {
    throw new Error(`Failed to update post with id ${post.id}`);
  }

  return response.json();
};

// 删除文章
export const deletePost = async (id: number): Promise<void> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));

  const response = await fetch(`https://jsonplaceholder.typicode.com/posts/${id}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    throw new Error(`Failed to delete post with id ${id}`);
  }
};

// 模拟可能失败的 API 调用
export const createPostWithError = async (data: CreatePostData): Promise<Post> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 30% 的概率失败
  if (Math.random() < 0.3) {
    throw new Error('服务器错误：创建文章失败');
  }

  return createPost(data);
};
