import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import { RouterProvider, createRouter } from '@tanstack/react-router';
import { QueryClientProvider, QueryClient } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// 导入生成的路由树
import { routeTree } from './routeTree.gen';

// 创建查询客户端
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 10, // 10 minutes
    },
  },
});

// 创建路由器
const router = createRouter({ routeTree });

// 注册路由器类型
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}

// 创建根元素
const rootElement = document.getElementById('root')!;
const root = createRoot(rootElement);

// 渲染应用
root.render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <RouterProvider router={router} />
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  </StrictMode>
);
