import { Link } from '@tanstack/react-router';
import { useState } from 'react';
import {
  usePosts,
  useCreatePost,
  useCreatePostOptimistic,
  useCreatePostWithError,
  useUpdatePost,
  useDeletePost,
  useInfinitePosts,
  usePrefetchPost,
} from '../hooks/usePosts';
import { type Post, type CreatePostData } from '../api/postsApi';

export function ReactQueryDemoPage() {
  const [currentTab, setCurrentTab] = useState<'basic' | 'optimistic' | 'error' | 'infinite'>(
    'basic'
  );
  const [newPost, setNewPost] = useState<CreatePostData>({
    title: '',
    body: '',
    userId: 1,
  });
  const [editingPost, setEditingPost] = useState<Post | null>(null);

  // 基础查询
  const postsQuery = usePosts(1, 5);

  // 无限滚动查询
  const infinitePostsQuery = useInfinitePosts(5);

  // 变更操作
  const createPostMutation = useCreatePost();
  const createPostOptimisticMutation = useCreatePostOptimistic();
  const createPostWithErrorMutation = useCreatePostWithError();
  const updatePostMutation = useUpdatePost();
  const deletePostMutation = useDeletePost();

  // 预取功能
  const prefetchPost = usePrefetchPost();

  // 处理创建文章
  const handleCreatePost = (mutation: typeof createPostMutation) => {
    if (!newPost.title.trim() || !newPost.body.trim()) return;

    mutation.mutate(newPost, {
      onSuccess: () => {
        setNewPost({ title: '', body: '', userId: 1 });
      },
    });
  };

  // 处理更新文章
  const handleUpdatePost = () => {
    if (!editingPost) return;

    updatePostMutation.mutate(editingPost, {
      onSuccess: () => {
        setEditingPost(null);
      },
    });
  };

  // 处理删除文章
  const handleDeletePost = (id: number) => {
    if (confirm('确定要删除这篇文章吗？')) {
      deletePostMutation.mutate(id);
    }
  };

  // 渲染文章列表
  const renderPostList = (posts: Post[], showActions = true) => (
    <div className="space-y-4">
      {posts.map(post => (
        <div key={post.id} className="card bg-base-100 shadow-sm border">
          <div className="card-body p-4">
            <h3 className="card-title text-lg">{post.title}</h3>
            <p className="text-sm text-gray-600 line-clamp-2">{post.body}</p>
            {showActions && (
              <div className="card-actions justify-end mt-2">
                <button
                  className="btn btn-ghost btn-xs"
                  onMouseEnter={() => prefetchPost(post.id)}
                  onClick={() => setEditingPost(post)}
                >
                  编辑
                </button>
                <button
                  className="btn btn-error btn-xs"
                  onClick={() => handleDeletePost(post.id)}
                  disabled={deletePostMutation.isPending}
                >
                  删除
                </button>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <Link to="/" className="btn btn-ghost mr-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-6 h-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18"
            />
          </svg>
          返回
        </Link>
        <h1 className="text-3xl font-bold">React Query 完整示例</h1>
      </div>

      {/* 标签页导航 */}
      <div className="tabs tabs-boxed mb-6">
        <button
          className={`tab ${currentTab === 'basic' ? 'tab-active' : ''}`}
          onClick={() => setCurrentTab('basic')}
        >
          基础功能
        </button>
        <button
          className={`tab ${currentTab === 'optimistic' ? 'tab-active' : ''}`}
          onClick={() => setCurrentTab('optimistic')}
        >
          乐观更新
        </button>
        <button
          className={`tab ${currentTab === 'error' ? 'tab-active' : ''}`}
          onClick={() => setCurrentTab('error')}
        >
          错误处理
        </button>
        <button
          className={`tab ${currentTab === 'infinite' ? 'tab-active' : ''}`}
          onClick={() => setCurrentTab('infinite')}
        >
          无限滚动
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：操作面板 */}
        <div className="space-y-6">
          {/* 创建文章表单 */}
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">创建新文章</h2>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">标题</span>
                </label>
                <input
                  type="text"
                  className="input input-bordered"
                  value={newPost.title}
                  onChange={e => setNewPost(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="输入文章标题..."
                />
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">内容</span>
                </label>
                <textarea
                  className="textarea textarea-bordered h-24"
                  value={newPost.body}
                  onChange={e => setNewPost(prev => ({ ...prev, body: e.target.value }))}
                  placeholder="输入文章内容..."
                />
              </div>

              <div className="card-actions justify-end">
                {currentTab === 'basic' && (
                  <button
                    className="btn btn-primary"
                    onClick={() => handleCreatePost(createPostMutation)}
                    disabled={createPostMutation.isPending}
                  >
                    {createPostMutation.isPending && (
                      <span className="loading loading-spinner loading-sm"></span>
                    )}
                    创建文章
                  </button>
                )}

                {currentTab === 'optimistic' && (
                  <button
                    className="btn btn-accent"
                    onClick={() => handleCreatePost(createPostOptimisticMutation)}
                    disabled={createPostOptimisticMutation.isPending}
                  >
                    {createPostOptimisticMutation.isPending && (
                      <span className="loading loading-spinner loading-sm"></span>
                    )}
                    乐观创建
                  </button>
                )}

                {currentTab === 'error' && (
                  <button
                    className="btn btn-warning"
                    onClick={() => handleCreatePost(createPostWithErrorMutation)}
                    disabled={createPostWithErrorMutation.isPending}
                  >
                    {createPostWithErrorMutation.isPending && (
                      <span className="loading loading-spinner loading-sm"></span>
                    )}
                    可能失败的创建
                  </button>
                )}
              </div>

              {/* 错误显示 */}
              {currentTab === 'error' && createPostWithErrorMutation.error && (
                <div className="alert alert-error mt-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="stroke-current shrink-0 h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span>{createPostWithErrorMutation.error.message}</span>
                </div>
              )}
            </div>
          </div>

          {/* 查询状态信息 */}
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">查询状态</h2>
              <div className="stats stats-vertical">
                <div className="stat">
                  <div className="stat-title">数据状态</div>
                  <div className="stat-value text-sm">
                    {postsQuery.isLoading
                      ? '加载中...'
                      : postsQuery.isError
                        ? '错误'
                        : postsQuery.isSuccess
                          ? '成功'
                          : '空闲'}
                  </div>
                </div>
                <div className="stat">
                  <div className="stat-title">缓存状态</div>
                  <div className="stat-value text-sm">
                    {postsQuery.isFetching ? '获取中' : postsQuery.isStale ? '过期' : '新鲜'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧：文章列表 */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">文章列表</h2>

            {currentTab !== 'infinite' ? (
              <>
                {postsQuery.isLoading && (
                  <div className="flex justify-center py-8">
                    <span className="loading loading-spinner loading-lg"></span>
                  </div>
                )}

                {postsQuery.isError && (
                  <div className="alert alert-error">
                    <span>加载文章失败</span>
                  </div>
                )}

                {postsQuery.isSuccess && renderPostList(postsQuery.data)}
              </>
            ) : (
              <>
                {infinitePostsQuery.data?.pages.map((page, pageIndex) => (
                  <div key={pageIndex}>{renderPostList(page, false)}</div>
                ))}

                <div className="text-center mt-4">
                  <button
                    className="btn btn-outline"
                    onClick={() => infinitePostsQuery.fetchNextPage()}
                    disabled={
                      !infinitePostsQuery.hasNextPage || infinitePostsQuery.isFetchingNextPage
                    }
                  >
                    {infinitePostsQuery.isFetchingNextPage ? (
                      <span className="loading loading-spinner loading-sm"></span>
                    ) : infinitePostsQuery.hasNextPage ? (
                      '加载更多'
                    ) : (
                      '没有更多数据'
                    )}
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* 编辑模态框 */}
      {editingPost && (
        <div className="modal modal-open">
          <div className="modal-box">
            <h3 className="font-bold text-lg mb-4">编辑文章</h3>

            <div className="form-control mb-4">
              <label className="label">
                <span className="label-text">标题</span>
              </label>
              <input
                type="text"
                className="input input-bordered"
                value={editingPost.title}
                onChange={e =>
                  setEditingPost(prev => (prev ? { ...prev, title: e.target.value } : null))
                }
              />
            </div>

            <div className="form-control mb-4">
              <label className="label">
                <span className="label-text">内容</span>
              </label>
              <textarea
                className="textarea textarea-bordered h-24"
                value={editingPost.body}
                onChange={e =>
                  setEditingPost(prev => (prev ? { ...prev, body: e.target.value } : null))
                }
              />
            </div>

            <div className="modal-action">
              <button className="btn" onClick={() => setEditingPost(null)}>
                取消
              </button>
              <button
                className="btn btn-primary"
                onClick={handleUpdatePost}
                disabled={updatePostMutation.isPending}
              >
                {updatePostMutation.isPending && (
                  <span className="loading loading-spinner loading-sm"></span>
                )}
                保存
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 特性说明 */}
      <div className="mt-8 bg-base-200 p-6 rounded-lg">
        <h3 className="font-bold text-lg mb-4">本示例展示的 React Query 特性</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <ul className="list-disc list-inside space-y-2">
            <li>
              <strong>基础查询:</strong> 数据获取、缓存、重新验证
            </li>
            <li>
              <strong>变更操作:</strong> 创建、更新、删除数据
            </li>
            <li>
              <strong>乐观更新:</strong> 立即更新 UI，失败时回滚
            </li>
            <li>
              <strong>错误处理:</strong> 重试机制、错误边界
            </li>
          </ul>
          <ul className="list-disc list-inside space-y-2">
            <li>
              <strong>无限滚动:</strong> 分页数据的无限加载
            </li>
            <li>
              <strong>预取数据:</strong> 鼠标悬停时预取详情
            </li>
            <li>
              <strong>查询状态:</strong> 加载、错误、成功状态管理
            </li>
            <li>
              <strong>缓存管理:</strong> 智能缓存失效和更新
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
