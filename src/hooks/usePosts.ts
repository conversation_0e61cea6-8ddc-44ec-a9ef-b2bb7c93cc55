import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import {
  fetchPosts,
  fetchPost,
  createPost,
  updatePost,
  deletePost,
  createPostWithError,
  type Post,
} from '../api/postsApi';

// 查询键工厂
export const postKeys = {
  all: ['posts'] as const,
  lists: () => [...postKeys.all, 'list'] as const,
  list: (page?: number, limit?: number) => [...postKeys.lists(), { page, limit }] as const,
  details: () => [...postKeys.all, 'detail'] as const,
  detail: (id: number) => [...postKeys.details(), id] as const,
  infinite: () => [...postKeys.all, 'infinite'] as const,
};

// 获取文章列表
export const usePosts = (page: number = 1, limit: number = 10) => {
  return useQuery({
    queryKey: postKeys.list(page, limit),
    queryFn: () => fetchPosts(page, limit),
    staleTime: 5 * 60 * 1000, // 5分钟内数据被认为是新鲜的
    gcTime: 10 * 60 * 1000, // 10分钟后从缓存中移除
  });
};

// 获取单个文章
export const usePost = (id: number) => {
  return useQuery({
    queryKey: postKeys.detail(id),
    queryFn: () => fetchPost(id),
    enabled: !!id, // 只有当 id 存在时才执行查询
  });
};

// 无限滚动文章列表
export const useInfinitePosts = (limit: number = 10) => {
  return useInfiniteQuery({
    queryKey: postKeys.infinite(),
    queryFn: ({ pageParam = 1 }) => fetchPosts(pageParam, limit),
    getNextPageParam: (lastPage, allPages) => {
      // 如果最后一页的数据少于 limit，说明没有更多数据了
      return lastPage.length === limit ? allPages.length + 1 : undefined;
    },
    initialPageParam: 1,
  });
};

// 创建文章（基础版本）
export const useCreatePost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createPost,
    onSuccess: newPost => {
      // 使相关查询失效
      queryClient.invalidateQueries({ queryKey: postKeys.lists() });
      queryClient.invalidateQueries({ queryKey: postKeys.infinite() });

      // 可选：直接更新缓存
      queryClient.setQueryData(postKeys.detail(newPost.id), newPost);
    },
    onError: error => {
      console.error('创建文章失败:', error);
    },
  });
};

// 创建文章（乐观更新版本）
export const useCreatePostOptimistic = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createPost,
    onMutate: async newPostData => {
      // 取消相关的查询以避免冲突
      await queryClient.cancelQueries({ queryKey: postKeys.lists() });

      // 获取当前数据的快照
      const previousPosts = queryClient.getQueryData(postKeys.list(1, 10));

      // 乐观更新：立即添加新文章到缓存
      const optimisticPost: Post = {
        id: Date.now(), // 临时 ID
        ...newPostData,
      };

      queryClient.setQueryData(postKeys.list(1, 10), (old: Post[] | undefined) => {
        return old ? [optimisticPost, ...old] : [optimisticPost];
      });

      // 返回上下文对象，包含回滚数据
      return { previousPosts, optimisticPost };
    },
    onError: (_error, _newPostData, context) => {
      // 发生错误时回滚
      if (context?.previousPosts) {
        queryClient.setQueryData(postKeys.list(1, 10), context.previousPosts);
      }
    },
    onSettled: () => {
      // 无论成功还是失败，都重新获取数据以确保一致性
      queryClient.invalidateQueries({ queryKey: postKeys.lists() });
    },
  });
};

// 创建文章（带错误处理）
export const useCreatePostWithError = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createPostWithError,
    retry: (failureCount, error) => {
      // 最多重试 2 次，且只对网络错误重试
      return failureCount < 2 && error.message.includes('网络');
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000), // 指数退避
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: postKeys.lists() });
    },
  });
};

// 更新文章
export const useUpdatePost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updatePost,
    onMutate: async updatedPost => {
      // 取消相关查询
      await queryClient.cancelQueries({
        queryKey: postKeys.detail(updatedPost.id),
      });

      // 获取当前数据快照
      const previousPost = queryClient.getQueryData(postKeys.detail(updatedPost.id));

      // 乐观更新
      queryClient.setQueryData(postKeys.detail(updatedPost.id), updatedPost);

      return { previousPost };
    },
    onError: (_error, updatedPost, context) => {
      // 回滚
      if (context?.previousPost) {
        queryClient.setQueryData(postKeys.detail(updatedPost.id), context.previousPost);
      }
    },
    onSettled: (_data, _error, updatedPost) => {
      // 重新获取数据
      queryClient.invalidateQueries({
        queryKey: postKeys.detail(updatedPost.id),
      });
      queryClient.invalidateQueries({ queryKey: postKeys.lists() });
    },
  });
};

// 删除文章
export const useDeletePost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deletePost,
    onMutate: async postId => {
      // 取消相关查询
      await queryClient.cancelQueries({ queryKey: postKeys.lists() });

      // 获取当前数据快照
      const previousPosts = queryClient.getQueryData(postKeys.list(1, 10));

      // 乐观更新：从列表中移除文章
      queryClient.setQueryData(postKeys.list(1, 10), (old: Post[] | undefined) => {
        return old ? old.filter(post => post.id !== postId) : [];
      });

      return { previousPosts };
    },
    onError: (_error, _postId, context) => {
      // 回滚
      if (context?.previousPosts) {
        queryClient.setQueryData(postKeys.list(1, 10), context.previousPosts);
      }
    },
    onSuccess: (_data, postId) => {
      // 从缓存中移除单个文章数据
      queryClient.removeQueries({ queryKey: postKeys.detail(postId) });
    },
    onSettled: () => {
      // 重新获取列表数据
      queryClient.invalidateQueries({ queryKey: postKeys.lists() });
    },
  });
};

// 预取文章详情
export const usePrefetchPost = () => {
  const queryClient = useQueryClient();

  return (id: number) => {
    queryClient.prefetchQuery({
      queryKey: postKeys.detail(id),
      queryFn: () => fetchPost(id),
      staleTime: 5 * 60 * 1000, // 5分钟
    });
  };
};
