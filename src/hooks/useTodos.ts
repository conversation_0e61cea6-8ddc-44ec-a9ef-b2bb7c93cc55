import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { fetchTodos, fetchTodoById, createTodo, updateTodo, deleteTodo } from '../api/todoApi';
import type { Todo } from '../api/todoApi';

// 查询键常量
export const todoKeys = {
  all: ['todos'] as const,
  lists: () => [...todoKeys.all, 'list'] as const,
  list: (filters: string) => [...todoKeys.lists(), { filters }] as const,
  details: () => [...todoKeys.all, 'detail'] as const,
  detail: (id: number) => [...todoKeys.details(), id] as const,
};

// 获取所有待办事项的Hook
export const useTodos = () => {
  return useQuery({
    queryKey: todoKeys.lists(),
    queryFn: fetchTodos,
  });
};

// 获取单个待办事项的Hook
export const useTodo = (id: number) => {
  return useQuery({
    queryKey: todoKeys.detail(id),
    queryFn: () => fetchTodoById(id),
    enabled: !!id, // 只有当id存在时才执行查询
  });
};

// 创建待办事项的Hook
export const useCreateTodo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (newTodo: Omit<Todo, 'id'>) => createTodo(newTodo),
    onSuccess: () => {
      // 创建成功后，使列表查询失效，触发重新获取
      queryClient.invalidateQueries({ queryKey: todoKeys.lists() });
    },
  });
};

// 更新待办事项的Hook
export const useUpdateTodo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateTodo,
    onSuccess: updatedTodo => {
      // 更新成功后，更新缓存中的数据
      queryClient.invalidateQueries({ queryKey: todoKeys.detail(updatedTodo.id) });
      queryClient.invalidateQueries({ queryKey: todoKeys.lists() });
    },
  });
};

// 删除待办事项的Hook
export const useDeleteTodo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteTodo,
    onSuccess: (_data, variables) => {
      // 删除成功后，从缓存中移除该项并更新列表
      queryClient.removeQueries({ queryKey: todoKeys.detail(variables) });
      queryClient.invalidateQueries({ queryKey: todoKeys.lists() });
    },
  });
};
