// 通用类型定义

// API 响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

// 分页参数
export interface PaginationParams {
  page: number;
  pageSize: number;
  total?: number;
}

// 分页响应
export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

// 表单状态
export interface FormState {
  isSubmitting: boolean;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
}

// 用户信息
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  role: UserRole;
  createdAt: string;
  updatedAt: string;
}

// 用户角色
export const UserRole = {
  ADMIN: 'admin',
  MERCHANT: 'merchant',
  USER: 'user',
} as const;

export type UserRole = (typeof UserRole)[keyof typeof UserRole];

// 商户信息
export interface Merchant {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  status: MerchantStatus;
  contactEmail: string;
  contactPhone?: string;
  address?: string;
  createdAt: string;
  updatedAt: string;
}

// 商户状态
export const MerchantStatus = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
  SUSPENDED: 'suspended',
} as const;

export type MerchantStatus = (typeof MerchantStatus)[keyof typeof MerchantStatus];

// 订单信息
export interface Order {
  id: string;
  merchantId: string;
  userId: string;
  amount: number;
  currency: string;
  status: OrderStatus;
  items: OrderItem[];
  createdAt: string;
  updatedAt: string;
}

// 订单状态
export const OrderStatus = {
  PENDING: 'pending',
  PAID: 'paid',
  SHIPPED: 'shipped',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',
} as const;

export type OrderStatus = (typeof OrderStatus)[keyof typeof OrderStatus];

// 订单项
export interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  price: number;
  total: number;
}

// 产品信息
export interface Product {
  id: string;
  merchantId: string;
  name: string;
  description?: string;
  price: number;
  currency: string;
  images: string[];
  category: string;
  stock: number;
  status: ProductStatus;
  createdAt: string;
  updatedAt: string;
}

// 产品状态
export const ProductStatus = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  OUT_OF_STOCK: 'out_of_stock',
  DISCONTINUED: 'discontinued',
} as const;

export type ProductStatus = (typeof ProductStatus)[keyof typeof ProductStatus];

// 通用选项类型
export interface Option<T = string> {
  label: string;
  value: T;
  disabled?: boolean;
}

// 表格列定义
export interface TableColumn<T = Record<string, unknown>> {
  key: keyof T;
  title: string;
  width?: number;
  sortable?: boolean;
  render?: (value: unknown, record: T, index: number) => React.ReactNode;
}

// 排序参数
export interface SortParams {
  field: string;
  order: 'asc' | 'desc';
}

// 筛选参数
export interface FilterParams {
  [key: string]: unknown;
}

// 搜索参数
export interface SearchParams extends PaginationParams {
  keyword?: string;
  sort?: SortParams;
  filters?: FilterParams;
}
